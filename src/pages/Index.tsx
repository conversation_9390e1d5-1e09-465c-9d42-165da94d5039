
import { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { ArrowRight, Lightbulb, Building, Factory, Home, Shield, Zap, Users, Award, Clock, Star, MessageCircle, Palette, Wrench, HeadphonesIcon } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { CategoryCard, CategoryCardSkeleton } from "@/components/ui/category";
import { fetchCategories } from "@/lib/data-fetch";
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';

interface Product {
  id: string;
  name: string;
  category: string;
  shortDescription: string;
  images: string[];
}

// Enhanced intersection observer hook inspired by 1-light.eu
const useScrollReveal = (options = {}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting && !hasAnimated) {
        setIsVisible(true);
        setHasAnimated(true);
      }
    }, {
      threshold: 0.1,
      rootMargin: '-50px 0px -50px 0px',
      ...options
    });

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [hasAnimated]);

  return [ref, isVisible] as const;
};

// Legacy hook for backward compatibility
const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting);
    }, { threshold: 0.1, ...options });

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, []);

  return [ref, isIntersecting] as const;
};

// Typewriter component
const TypewriterText = ({ text, delay = 50, className = "" }: { text: string; delay?: number; className?: string }) => {
  const [displayText, setDisplayText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);
  const [ref, isVisible] = useIntersectionObserver();

  useEffect(() => {
    if (!isVisible) return;

    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, delay);
      return () => clearTimeout(timeout);
    }
  }, [currentIndex, text, delay, isVisible]);

  return (
    <div ref={ref} className={className}>
      <span>{displayText}</span>
      <span className={`inline-block w-0.5 h-8 bg-current ml-1 ${currentIndex < text.length ? 'animate-blink' : 'opacity-0'}`} />
    </div>
  );
};

// Word reveal component
const WordReveal = ({ text, className = "" }: { text: string; className?: string }) => {
  const words = text.split(' ');
  const [ref, isVisible] = useIntersectionObserver();

  return (
    <div ref={ref} className={className}>
      {words.map((word, index) => (
        <span
          key={index}
          className={`inline-block mr-2 ${isVisible ? `animate-stagger-${Math.min(index % 6 + 1, 6)}` : 'opacity-0'} motion-reduce:animate-fade-in`}
        >
          {word}
        </span>
      ))}
    </div>
  );
};

// Scroll-triggered reveal components inspired by 1-light.eu
const ScrollReveal = ({
  children,
  animation = 'reveal-up',
  delay = 0,
  className = ""
}: {
  children: React.ReactNode;
  animation?: string;
  delay?: number;
  className?: string;
}) => {
  const [ref, isVisible] = useScrollReveal();

  return (
    <div
      ref={ref}
      className={`${className} ${isVisible ? animation : 'opacity-0'} motion-reduce:animate-fade-in`}
      style={{ animationDelay: `${delay}ms` }}
    >
      {children}
    </div>
  );
};

const SectionReveal = ({ children, className = "" }: { children: React.ReactNode; className?: string }) => {
  const [ref, isVisible] = useScrollReveal();

  return (
    <section
      ref={ref}
      className={`${className} ${isVisible ? 'animate-section-reveal' : 'opacity-0'} motion-reduce:animate-fade-in`}
    >
      {children}
    </section>
  );
};

const Index = () => {
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [isCategoriesLoading, setIsCategoriesLoading] = useState(true);
  const [categories, setCategories] = useState<any[]>([]);

  useEffect(() => {
    // Load featured products
    fetch("/data/products.json")
      .then((res) => res.json())
      .then((products: Product[]) => {
        setFeaturedProducts(products.slice(0, 3));
      })
      .catch((error) => console.error("Error loading products:", error));
  }, []);

  useEffect(() => {
    fetchCategories()
      .then((data) => {
        setCategories(data);
        setIsCategoriesLoading(false);
      })
      .catch((error) => {
        console.error("Error fetching categories:", error);
        setIsCategoriesLoading(false);
      });
  }, []);


  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center bg-gradient-to-r from-black to-gray-900 text-white overflow-hidden">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30 animate-float"
          style={{
            backgroundImage: "url('https://images.unsplash.com/photo-1524484485831-a92ffc0de03f?w=1920&h=1080&fit=crop')"
          }}
        />
        {/* Floating light orbs for premium effect */}
        <div className="absolute top-20 left-20 w-32 h-32 bg-white/5 rounded-full blur-xl animate-float-delayed"></div>
        <div className="absolute bottom-32 right-32 w-24 h-24 bg-white/3 rounded-full blur-lg animate-float"></div>
        <div className="absolute top-1/2 left-10 w-16 h-16 bg-white/4 rounded-full blur-md animate-glow-pulse"></div>

        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 animate-hero-title motion-reduce:animate-fade-in">
            <span className="inline-block">Premium</span>{" "}
            <span className="inline-block bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Architectural
            </span>{" "}
            <span className="inline-block">Lighting</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-gray-300 animate-hero-subtitle motion-reduce:animate-fade-in">
            Illuminating excellence with cutting-edge LED technology and innovative design solutions
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center animate-hero-cta motion-reduce:animate-fade-in">
            <Button asChild size="lg" className="bg-white text-black hover:bg-gray-700 hover:text-white transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
              <Link to="/categories">
                Explore Products
                <ArrowRight className="ml-2 w-5 h-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="border-white text-black hover:bg-black hover:text-white transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
              <Link to="/contact">Get Quote</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Brand Intro */}
      <SectionReveal className="py-20 bg-white relative overflow-hidden">
        {/* Subtle background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 w-64 h-64 bg-black rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-10 right-10 w-48 h-48 bg-gray-800 rounded-full blur-2xl animate-float-delayed"></div>
        </div>

        <div className="max-w-6xl mx-auto px-4 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="text-center lg:text-left">
              <ScrollReveal animation="animate-content-reveal" delay={200}>
                <TypewriterText
                  text="Lighting Innovation Since 2010"
                  delay={80}
                  className="text-4xl font-bold text-black mb-6 h-16 flex items-center justify-center lg:justify-start"
                />
              </ScrollReveal>
              <ScrollReveal animation="animate-reveal-up" delay={600}>
                <WordReveal
                  text="LumenX combines advanced LED technology with sophisticated design to create lighting solutions that transform spaces. From residential interiors to large-scale commercial projects, we deliver excellence in every fixture."
                  className="text-lg text-gray-600 leading-relaxed mb-8"
                />
              </ScrollReveal>
              <ScrollReveal animation="animate-reveal-up" delay={800}>
                <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                  <Button asChild size="lg" className="bg-black text-white hover:bg-gray-800 transition-all duration-300">
                    <Link to="/about">
                      Learn More About Us
                      <ArrowRight className="ml-2 w-5 h-5" />
                    </Link>
                  </Button>
                  <Button asChild variant="outline" size="lg" className="hover:bg-black hover:text-white transition-all duration-300">
                    <Link to="/portfolio">View Portfolio</Link>
                  </Button>
                </div>
              </ScrollReveal>
            </div>

            <ScrollReveal animation="animate-reveal-right" delay={400}>
              <div className="relative group">
                {/* Video placeholder with fallback image */}
                <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                  <img
                    src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=400&fit=crop"
                    alt="LumenX Manufacturing Process"
                    className="w-full h-80 object-cover"
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-black/40 flex items-center justify-center group-hover:bg-black/30 transition-all duration-300">
                    <button className="bg-white/90 backdrop-blur-sm rounded-full p-4 hover:bg-white hover:scale-110 transition-all duration-300 group">
                      <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center">
                        <div className="w-0 h-0 border-l-[8px] border-l-white border-y-[6px] border-y-transparent ml-1"></div>
                      </div>
                    </button>
                  </div>
                  <div className="absolute bottom-4 left-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                    <p className="text-sm font-medium text-black">Watch: Inside LumenX Manufacturing</p>
                    <p className="text-xs text-gray-600">See how we create premium lighting solutions</p>
                  </div>
                </div>
              </div>
            </ScrollReveal>
          </div>
        </div>
      </SectionReveal>

      {/* Featured Categories */}
      <SectionReveal className="py-20 bg-gray-50 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-transparent via-black/10 to-transparent"></div>

        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <ScrollReveal animation="animate-content-reveal">
              <h2 className="text-4xl font-bold text-black mb-4">Our Categories</h2>
            </ScrollReveal>
            <ScrollReveal animation="animate-reveal-up" delay={200}>
              <p className="text-lg text-gray-600">Discover our comprehensive range of lighting solutions</p>
            </ScrollReveal>
          </div>

          <div className="mt-8">
            {isCategoriesLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {[1, 2, 3, 4].map((i) => (
                  <div
                    key={i}
                    className={`animate-wave-${i} motion-reduce:animate-fade-in transform-gpu`}
                    style={{
                      animationFillMode: 'both',
                      animationDelay: `${(i - 1) * 0.1}s`
                    }}
                  >
                    <CategoryCardSkeleton />
                  </div>
                ))}
              </div>
            ) : (
              <div className="relative">
                <Swiper
                  modules={[Navigation]}
                  spaceBetween={30}
                  slidesPerView={1}
                  navigation={{
                    nextEl: '.swiper-button-next-custom',
                    prevEl: '.swiper-button-prev-custom',
                  }}
                  breakpoints={{
                    640: {
                      slidesPerView: 2,
                    },
                    1024: {
                      slidesPerView: 3,
                    },
                    1280: {
                      slidesPerView: 4,
                    },
                  }}
                  className="pb-12"
                >
                  {categories.map((category, index) => (
                    <SwiperSlide key={index}>
                      <div
                        className={`
                          ${index % 2 === 0 ? 'animate-slide-in-left' : 'animate-slide-in-right'}
                          motion-reduce:animate-fade-in
                          transition-all duration-300 ease-out
                          hover:scale-[1.02]
                          hover:shadow-2xl
                          transform-gpu
                        `}
                        style={{
                          animationFillMode: 'both',
                          animationDelay: `${index * 0.1}s`
                        }}
                      >
                        <CategoryCard category={{ ...category, id: category.name.toLowerCase() }} />
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>
                <Button
                  className="swiper-button-prev-custom absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white text-black rounded-full p-2 shadow-md -ml-4 transform hover:scale-110 transition-all duration-300 animate-slide-in-left"
                  size="icon"
                  variant="ghost"
                >
                  <ArrowRight className="w-5 h-5 rotate-180 transition-transform group-hover:-translate-x-1" />
                </Button>
                <Button
                  className="swiper-button-next-custom absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white text-black rounded-full p-2 shadow-md -mr-4 transform hover:scale-110 transition-all duration-300 animate-slide-in-right"
                  size="icon"
                  variant="ghost"
                >
                  <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
                </Button>
              </div>
            )}
          </div>
        </div>
      </SectionReveal>

      {/* Featured Products */}
      <SectionReveal className="py-20 bg-white relative">
        {/* Subtle grid pattern background */}
        <div className="absolute inset-0 opacity-5" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgba(0,0,0,0.15) 1px, transparent 0)`,
          backgroundSize: '20px 20px'
        }}></div>

        <div className="max-w-7xl mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <ScrollReveal animation="animate-content-reveal">
              <h2 className="text-4xl font-bold text-black mb-4">Featured Products</h2>
            </ScrollReveal>
            <ScrollReveal animation="animate-reveal-up" delay={200}>
              <p className="text-lg text-gray-600">Discover our most popular lighting solutions</p>
            </ScrollReveal>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {featuredProducts.map((product, index) => {
              // Cycle through different reveal animations inspired by 1-light.eu
              const revealAnimations = [
                'animate-reveal-left',
                'animate-reveal-up',
                'animate-reveal-right'
              ];
              const revealAnimation = revealAnimations[index % 3];

              return (
                <ScrollReveal
                  key={product.id}
                  animation={revealAnimation}
                  delay={index * 200}
                  className="group"
                >
                  <Link to={`/product/${product.id}`}>
                    <Card className="overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 hover:rotate-1 transform-gpu">
                      <div className="relative h-64 overflow-hidden">
                        <img
                          src={product.images[0]}
                          alt={product.name}
                          className="w-full h-full object-cover group-hover:scale-125 transition-transform duration-700 transform-gpu"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500" />
                        {/* Floating light effect on hover */}
                        <div className="absolute top-4 right-4 w-8 h-8 bg-white/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 group-hover:animate-float transition-all duration-500"></div>
                      </div>
                      <CardContent className="p-6 relative">
                        <h3 className="text-xl font-semibold text-black mb-2 group-hover:text-gray-800 transition-colors">{product.name}</h3>
                        <p className="text-gray-600 mb-4 group-hover:text-gray-700 transition-colors">{product.shortDescription}</p>
                        <Button variant="outline" className="w-full group-hover:bg-black group-hover:text-white group-hover:shadow-lg transform group-hover:scale-105 transition-all duration-300">
                          View Details
                          <ArrowRight className="ml-2 w-4 h-4 transition-transform group-hover:translate-x-2" />
                        </Button>
                        {/* Subtle glow effect */}
                        <div className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 group-hover:animate-glow-pulse transition-opacity duration-500 pointer-events-none"></div>
                      </CardContent>
                    </Card>
                  </Link>
                </ScrollReveal>
              );
            })}
          </div>
        </div>
      </SectionReveal>

      {/* Why Choose LumenX */}
      <SectionReveal className="py-20 bg-white relative">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <ScrollReveal animation="animate-content-reveal">
              <h2 className="text-4xl font-bold text-black mb-4">Why Choose LumenX?</h2>
            </ScrollReveal>
            <ScrollReveal animation="animate-reveal-up" delay={200}>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                We're not just another lighting company. We're your partners in creating exceptional lighting experiences that transform spaces and enhance lives.
              </p>
            </ScrollReveal>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Shield,
                title: "15-Year Warranty",
                description: "Industry-leading warranty coverage on all our LED products, ensuring long-term peace of mind and reliability.",
                color: "text-blue-600"
              },
              {
                icon: Zap,
                title: "Energy Efficient",
                description: "Up to 80% energy savings compared to traditional lighting, reducing your carbon footprint and electricity bills.",
                color: "text-yellow-600"
              },
              {
                icon: Users,
                title: "Expert Support",
                description: "Dedicated lighting consultants and technical support team available 24/7 to assist with your projects.",
                color: "text-green-600"
              },
              {
                icon: Award,
                title: "Award-Winning Design",
                description: "Multiple international design awards for innovation, quality, and aesthetic excellence in lighting solutions.",
                color: "text-purple-600"
              },
              {
                icon: Clock,
                title: "24-Hour Delivery",
                description: "Fast, reliable delivery service ensuring your lighting products arrive exactly when you need them.",
                color: "text-red-600"
              },
              {
                icon: Lightbulb,
                title: "Innovation First",
                description: "Cutting-edge LED technology and smart lighting solutions that adapt to your lifestyle and business needs.",
                color: "text-orange-600"
              }
            ].map((feature, index) => (
              <ScrollReveal
                key={feature.title}
                animation="animate-icon-bounce"
                delay={index * 100}
                className="group"
              >
                <div className="text-center p-6 rounded-xl hover:bg-gray-50 transition-all duration-300 hover:shadow-lg">
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4 group-hover:scale-110 transition-transform duration-300 ${feature.color}`}>
                    <feature.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-semibold text-black mb-3 group-hover:text-gray-800 transition-colors">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </ScrollReveal>
            ))}
          </div>

          <ScrollReveal animation="animate-reveal-up" delay={800}>
            <div className="text-center mt-16">
              <div className="bg-gradient-to-r from-black to-gray-800 rounded-2xl p-8 text-white">
                <h3 className="text-2xl font-bold mb-4">Ready to Experience the LumenX Difference?</h3>
                <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
                  Join over 10,000 satisfied customers who have transformed their spaces with our premium lighting solutions.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-black hover:bg-gray-200 transition-all duration-300">
                    <Link to="/contact">
                      Get Free Consultation
                      <ArrowRight className="ml-2 w-5 h-5" />
                    </Link>
                  </Button>
                  <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-black transition-all duration-300">
                    <Link to="/categories">Browse Products</Link>
                  </Button>
                </div>
              </div>
            </div>
          </ScrollReveal>
        </div>
      </SectionReveal>

      {/* Popular Products */}
      <SectionReveal className="py-20 bg-gray-50 relative">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <ScrollReveal animation="animate-content-reveal">
              <h2 className="text-4xl font-bold text-black mb-4">Popular Products</h2>
            </ScrollReveal>
            <ScrollReveal animation="animate-reveal-up" delay={200}>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Discover our best-selling lighting solutions trusted by thousands of customers worldwide
              </p>
            </ScrollReveal>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                name: "LED Panel Pro 600x600",
                category: "Office Lighting",
                image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop",
                price: "$89.99",
                rating: 4.8,
                sales: "2,340 sold"
              },
              {
                name: "Smart Track Spotlight",
                category: "Track Lighting",
                image: "https://images.unsplash.com/photo-1524484485831-a92ffc0de03f?w=400&h=300&fit=crop",
                price: "$124.99",
                rating: 4.9,
                sales: "1,890 sold"
              },
              {
                name: "Industrial High Bay 150W",
                category: "Industrial",
                image: "https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=400&h=300&fit=crop",
                price: "$199.99",
                rating: 4.7,
                sales: "1,560 sold"
              },
              {
                name: "Decorative Pendant Luna",
                category: "Decorative",
                image: "https://images.unsplash.com/photo-1513506003901-1e6a229e2d15?w=400&h=300&fit=crop",
                price: "$159.99",
                rating: 4.9,
                sales: "2,100 sold"
              }
            ].map((product, index) => (
              <ScrollReveal
                key={product.name}
                animation={`animate-card-flip-delay-${index + 1}`}
                className="group"
              >
                <Card className="overflow-hidden hover:shadow-xl transition-all duration-500 hover:-translate-y-2 transform-gpu">
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                    />
                    <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 text-xs font-semibold text-gray-800">
                      {product.sales}
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs text-gray-500 uppercase tracking-wide">{product.category}</span>
                      <div className="flex items-center space-x-1">
                        <span className="text-yellow-400">★</span>
                        <span className="text-sm text-gray-600">{product.rating}</span>
                      </div>
                    </div>
                    <h3 className="text-lg font-semibold text-black mb-2 group-hover:text-gray-800 transition-colors">
                      {product.name}
                    </h3>
                    <div className="flex items-center justify-between">
                      <span className="text-xl font-bold text-black">{product.price}</span>
                      <Button size="sm" variant="outline" className="group-hover:bg-black group-hover:text-white transition-all duration-300">
                        View Details
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </ScrollReveal>
            ))}
          </div>

          <ScrollReveal animation="animate-reveal-up" delay={800}>
            <div className="text-center mt-12">
              <Button asChild size="lg" variant="outline" className="hover:bg-black hover:text-white transition-all duration-300">
                <Link to="/categories">
                  View All Products
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
              </Button>
            </div>
          </ScrollReveal>
        </div>
      </SectionReveal>

      {/* Our Process */}
      <SectionReveal className="py-20 bg-white relative">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <ScrollReveal animation="animate-content-reveal">
              <h2 className="text-4xl font-bold text-black mb-4">Our Process</h2>
            </ScrollReveal>
            <ScrollReveal animation="animate-reveal-up" delay={200}>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                From initial consultation to ongoing support, we guide you through every step of your lighting transformation journey.
              </p>
            </ScrollReveal>
          </div>

          <div className="relative">
            {/* Process line */}
            <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-gray-200 via-gray-400 to-gray-200 transform -translate-y-1/2"></div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  step: "01",
                  icon: MessageCircle,
                  title: "Consultation",
                  description: "We start with understanding your needs, space requirements, and lighting goals through detailed consultation.",
                  color: "bg-blue-500"
                },
                {
                  step: "02",
                  icon: Palette,
                  title: "Design",
                  description: "Our experts create custom lighting designs with 3D visualizations and technical specifications tailored to your space.",
                  color: "bg-green-500"
                },
                {
                  step: "03",
                  icon: Wrench,
                  title: "Installation",
                  description: "Professional installation by certified technicians ensures perfect placement and optimal performance of your lighting system.",
                  color: "bg-orange-500"
                },
                {
                  step: "04",
                  icon: HeadphonesIcon,
                  title: "Support",
                  description: "Ongoing maintenance, technical support, and warranty coverage to keep your lighting performing at its best.",
                  color: "bg-purple-500"
                }
              ].map((process, index) => (
                <ScrollReveal
                  key={process.step}
                  animation={`animate-process-step-delay-${index + 1}`}
                  className="group relative"
                >
                  <div className="text-center">
                    {/* Step number */}
                    <div className="relative mb-6">
                      <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full ${process.color} text-white font-bold text-lg mb-4 group-hover:scale-110 transition-transform duration-300 relative z-10`}>
                        {process.step}
                      </div>
                      {/* Icon overlay */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-20 h-20 rounded-full bg-white shadow-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:scale-110">
                          <process.icon className="w-8 h-8 text-gray-700" />
                        </div>
                      </div>
                    </div>

                    <h3 className="text-xl font-semibold text-black mb-3 group-hover:text-gray-800 transition-colors">
                      {process.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {process.description}
                    </p>
                  </div>
                </ScrollReveal>
              ))}
            </div>
          </div>

          <ScrollReveal animation="animate-reveal-up" delay={1000}>
            <div className="text-center mt-16">
              <div className="bg-gray-50 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-black mb-4">Ready to Start Your Lighting Project?</h3>
                <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                  Our team of lighting experts is ready to help you create the perfect lighting solution for your space.
                </p>
                <Button asChild size="lg" className="bg-black text-white hover:bg-gray-800 transition-all duration-300">
                  <Link to="/contact">
                    Start Your Project
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </Link>
                </Button>
              </div>
            </div>
          </ScrollReveal>
        </div>
      </SectionReveal>

      {/* Customer Testimonials */}
      <SectionReveal className="py-20 bg-gray-900 text-white relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-20 w-64 h-64 bg-white rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-20 w-48 h-48 bg-white rounded-full blur-2xl animate-float-delayed"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <ScrollReveal animation="animate-content-reveal">
              <h2 className="text-4xl font-bold mb-4">What Our Customers Say</h2>
            </ScrollReveal>
            <ScrollReveal animation="animate-reveal-up" delay={200}>
              <p className="text-lg text-gray-300 max-w-2xl mx-auto">
                Don't just take our word for it. Here's what lighting professionals and homeowners say about LumenX.
              </p>
            </ScrollReveal>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "Sarah Johnson",
                role: "Interior Designer",
                company: "Modern Spaces Studio",
                image: "https://images.unsplash.com/photo-1494790108755-2616b9e0e4e4?w=100&h=100&fit=crop&crop=face",
                rating: 5,
                testimonial: "LumenX has completely transformed how I approach lighting design. Their LED panels provide the perfect balance of functionality and aesthetics. My clients are always impressed with the results."
              },
              {
                name: "Michael Chen",
                role: "Facility Manager",
                company: "TechCorp Industries",
                image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
                rating: 5,
                testimonial: "We upgraded our entire office complex with LumenX solutions. The energy savings have been remarkable - 70% reduction in lighting costs while improving workplace productivity."
              },
              {
                name: "Emma Rodriguez",
                role: "Homeowner",
                company: "Residential Customer",
                image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
                rating: 5,
                testimonial: "The smart lighting system from LumenX has made our home so much more comfortable and efficient. The installation was seamless and the support team was incredibly helpful."
              }
            ].map((testimonial, index) => (
              <ScrollReveal
                key={testimonial.name}
                animation="animate-testimonial-slide"
                delay={index * 200}
                className="group"
              >
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 hover:bg-white/15 transition-all duration-300 hover:scale-105 transform-gpu">
                  <div className="flex items-center mb-4">
                    <img
                      src={testimonial.image}
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full object-cover mr-4"
                    />
                    <div>
                      <h4 className="font-semibold text-white">{testimonial.name}</h4>
                      <p className="text-sm text-gray-300">{testimonial.role}</p>
                      <p className="text-xs text-gray-400">{testimonial.company}</p>
                    </div>
                  </div>

                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                    ))}
                  </div>

                  <p className="text-gray-200 leading-relaxed italic">
                    "{testimonial.testimonial}"
                  </p>
                </div>
              </ScrollReveal>
            ))}
          </div>

          <ScrollReveal animation="animate-reveal-up" delay={800}>
            <div className="text-center mt-16">
              <div className="flex items-center justify-center space-x-8 mb-8">
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">4.9/5</div>
                  <div className="text-sm text-gray-300">Average Rating</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">10,000+</div>
                  <div className="text-sm text-gray-300">Happy Customers</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">50,000+</div>
                  <div className="text-sm text-gray-300">Projects Completed</div>
                </div>
              </div>

              <Button asChild size="lg" className="bg-white text-black hover:bg-gray-200 transition-all duration-300">
                <Link to="/testimonials">
                  Read More Reviews
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
              </Button>
            </div>
          </ScrollReveal>
        </div>
      </SectionReveal>

      {/* Industry Applications */}
      <SectionReveal className="py-20 bg-gray-50 relative">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <ScrollReveal animation="animate-content-reveal">
              <h2 className="text-4xl font-bold text-black mb-4">Industry Applications</h2>
            </ScrollReveal>
            <ScrollReveal animation="animate-reveal-up" delay={200}>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                From cozy homes to large industrial facilities, our lighting solutions are trusted across diverse industries and applications.
              </p>
            </ScrollReveal>
          </div>

          <div className="space-y-16">
            {[
              {
                title: "Residential Lighting",
                subtitle: "Transform Your Home",
                description: "Create the perfect ambiance for every room with our residential lighting solutions. From warm living spaces to functional kitchens, we help homeowners achieve their ideal lighting environment.",
                image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop",
                features: ["Smart Home Integration", "Energy Efficient LEDs", "Dimming Controls", "Decorative Options"],
                stats: { projects: "5,000+", satisfaction: "98%" },
                reverse: false
              },
              {
                title: "Commercial Spaces",
                subtitle: "Enhance Productivity",
                description: "Boost workplace productivity and create impressive commercial environments with our professional lighting systems. Perfect for offices, retail stores, restaurants, and hospitality venues.",
                image: "https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop",
                features: ["Office Lighting", "Retail Displays", "Restaurant Ambiance", "Hotel Lighting"],
                stats: { projects: "3,500+", satisfaction: "99%" },
                reverse: true
              },
              {
                title: "Industrial Solutions",
                subtitle: "Built for Performance",
                description: "Robust, high-performance lighting for warehouses, factories, and industrial facilities. Our solutions meet the demanding requirements of industrial environments while maximizing energy efficiency.",
                image: "https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=600&h=400&fit=crop",
                features: ["High Bay Lighting", "Explosion Proof", "Motion Sensors", "Emergency Lighting"],
                stats: { projects: "1,500+", satisfaction: "97%" },
                reverse: false
              }
            ].map((application, index) => (
              <div key={application.title} className={`flex flex-col ${application.reverse ? 'lg:flex-row-reverse' : 'lg:flex-row'} items-center gap-12`}>
                <ScrollReveal
                  animation={application.reverse ? "animate-reveal-right" : "animate-reveal-left"}
                  delay={200}
                  className="lg:w-1/2"
                >
                  <div className="relative group">
                    <img
                      src={application.image}
                      alt={application.title}
                      className="w-full h-80 object-cover rounded-2xl shadow-lg group-hover:shadow-2xl transition-all duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    {/* Stats overlay */}
                    <div className="absolute bottom-4 left-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-4 transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                      <div className="flex justify-between text-center">
                        <div>
                          <div className="text-2xl font-bold text-black">{application.stats.projects}</div>
                          <div className="text-sm text-gray-600">Projects</div>
                        </div>
                        <div>
                          <div className="text-2xl font-bold text-black">{application.stats.satisfaction}</div>
                          <div className="text-sm text-gray-600">Satisfaction</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ScrollReveal>

                <ScrollReveal
                  animation={application.reverse ? "animate-reveal-left" : "animate-reveal-right"}
                  delay={400}
                  className="lg:w-1/2"
                >
                  <div className={`${application.reverse ? 'lg:pr-8' : 'lg:pl-8'}`}>
                    <div className="text-sm text-gray-500 uppercase tracking-wide mb-2">{application.subtitle}</div>
                    <h3 className="text-3xl font-bold text-black mb-4">{application.title}</h3>
                    <p className="text-gray-600 leading-relaxed mb-6">
                      {application.description}
                    </p>

                    <div className="grid grid-cols-2 gap-3 mb-6">
                      {application.features.map((feature, featureIndex) => (
                        <div key={feature} className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-black rounded-full"></div>
                          <span className="text-sm text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>

                    <Button asChild variant="outline" className="hover:bg-black hover:text-white transition-all duration-300">
                      <Link to={`/industries/${application.title.toLowerCase().replace(' ', '-')}`}>
                        Learn More
                        <ArrowRight className="ml-2 w-4 h-4" />
                      </Link>
                    </Button>
                  </div>
                </ScrollReveal>
              </div>
            ))}
          </div>
        </div>
      </SectionReveal>

      {/* Latest News & Insights */}
      <SectionReveal className="py-20 bg-white relative">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <ScrollReveal animation="animate-content-reveal">
              <h2 className="text-4xl font-bold text-black mb-4">Latest News & Insights</h2>
            </ScrollReveal>
            <ScrollReveal animation="animate-reveal-up" delay={200}>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Stay updated with the latest lighting trends, technology innovations, and company news from LumenX.
              </p>
            </ScrollReveal>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                category: "Technology",
                title: "The Future of Smart Lighting: AI-Powered Solutions",
                excerpt: "Discover how artificial intelligence is revolutionizing lighting control systems and creating more intuitive, energy-efficient environments.",
                image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=250&fit=crop",
                date: "March 15, 2024",
                readTime: "5 min read",
                author: "Dr. Sarah Chen"
              },
              {
                category: "Sustainability",
                title: "Achieving Carbon Neutrality with LED Lighting",
                excerpt: "Learn how businesses are reducing their carbon footprint by up to 80% through strategic LED lighting upgrades and smart controls.",
                image: "https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?w=400&h=250&fit=crop",
                date: "March 10, 2024",
                readTime: "4 min read",
                author: "Michael Rodriguez"
              },
              {
                category: "Design",
                title: "2024 Lighting Design Trends: What's Hot This Year",
                excerpt: "Explore the top lighting design trends shaping modern spaces, from minimalist fixtures to bold statement pieces.",
                image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=250&fit=crop",
                date: "March 5, 2024",
                readTime: "6 min read",
                author: "Emma Thompson"
              }
            ].map((article, index) => (
              <ScrollReveal
                key={article.title}
                animation="animate-card-flip"
                delay={index * 150}
                className="group"
              >
                <Link to={`/blog/${article.title.toLowerCase().replace(/[^a-z0-9]+/g, '-')}`}>
                  <Card className="overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 transform-gpu h-full">
                    <div className="relative h-48 overflow-hidden">
                      <img
                        src={article.image}
                        alt={article.title}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                      />
                      <div className="absolute top-3 left-3 bg-black/80 backdrop-blur-sm rounded-full px-3 py-1 text-xs font-semibold text-white">
                        {article.category}
                      </div>
                      <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </div>
                    <CardContent className="p-6 flex flex-col flex-grow">
                      <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
                        <span>{article.date}</span>
                        <span>{article.readTime}</span>
                      </div>

                      <h3 className="text-xl font-semibold text-black mb-3 group-hover:text-gray-800 transition-colors line-clamp-2">
                        {article.title}
                      </h3>

                      <p className="text-gray-600 leading-relaxed mb-4 flex-grow line-clamp-3">
                        {article.excerpt}
                      </p>

                      <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                        <span className="text-sm text-gray-500">By {article.author}</span>
                        <div className="flex items-center text-black group-hover:translate-x-1 transition-transform duration-300">
                          <span className="text-sm font-medium mr-1">Read More</span>
                          <ArrowRight className="w-4 h-4" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              </ScrollReveal>
            ))}
          </div>

          <ScrollReveal animation="animate-reveal-up" delay={600}>
            <div className="text-center mt-12">
              <Button asChild size="lg" variant="outline" className="hover:bg-black hover:text-white transition-all duration-300">
                <Link to="/blog">
                  View All Articles
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
              </Button>
            </div>
          </ScrollReveal>
        </div>
      </SectionReveal>

      {/* Call to Action */}
      <SectionReveal className="py-20 bg-black text-white relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-20 w-64 h-64 bg-white/3 rounded-full blur-2xl animate-float-delayed"></div>
          <div className="absolute top-1/2 left-1/4 w-32 h-32 bg-white/4 rounded-full blur-xl animate-glow-pulse"></div>
          <div className="absolute bottom-1/3 right-1/3 w-48 h-48 bg-white/2 rounded-full blur-2xl animate-float"></div>
        </div>

        {/* Subtle grid overlay */}
        <div className="absolute inset-0 opacity-10" style={{
          backgroundImage: `linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)`,
          backgroundSize: '50px 50px'
        }}></div>

        <div className="max-w-4xl mx-auto px-4 text-center relative z-10">
          <ScrollReveal animation="animate-content-reveal">
            <h2 className="text-4xl font-bold mb-6">
              Ready to Transform Your Space?
            </h2>
          </ScrollReveal>
          <ScrollReveal animation="animate-reveal-up" delay={300}>
            <p className="text-xl text-gray-300 mb-8">
              Let our lighting experts help you create the perfect ambiance for your project
            </p>
          </ScrollReveal>
          <ScrollReveal animation="animate-reveal-up" delay={600}>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-black hover:bg-gray-200 transform hover:scale-110 hover:shadow-2xl transition-all duration-300 group">
                <Link to="/contact">
                  Get Free Consultation
                  <ArrowRight className="ml-2 w-5 h-5 transition-transform group-hover:translate-x-2" />
                </Link>
              </Button>
            </div>
          </ScrollReveal>

          {/* Decorative elements */}
          <ScrollReveal animation="animate-reveal-up" delay={900}>
            <div className="mt-12 flex justify-center space-x-8 opacity-30">
              <div className="w-2 h-2 bg-white rounded-full animate-float"></div>
              <div className="w-2 h-2 bg-white rounded-full animate-float-delayed"></div>
              <div className="w-2 h-2 bg-white rounded-full animate-float"></div>
            </div>
          </ScrollReveal>
        </div>
      </SectionReveal>
    </div>
  );
};

export default Index;
