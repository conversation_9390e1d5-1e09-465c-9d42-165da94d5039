
import { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { fetchCategories } from "@/lib/data-fetch";
import 'swiper/css';
import 'swiper/css/navigation';

interface Product {
  id: string;
  name: string;
  category: string;
  shortDescription: string;
  images: string[];
}

// Enhanced intersection observer hook inspired by 1-light.eu
const useScrollReveal = (options = {}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting && !hasAnimated) {
        setIsVisible(true);
        setHasAnimated(true);
      }
    }, {
      threshold: 0.1,
      rootMargin: '-50px 0px -50px 0px',
      ...options
    });

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [hasAnimated]);

  return [ref, isVisible] as const;
};

// Legacy hook for backward compatibility
const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting);
    }, { threshold: 0.1, ...options });

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, []);

  return [ref, isIntersecting] as const;
};

// Typewriter component
const TypewriterText = ({ text, delay = 50, className = "" }: { text: string; delay?: number; className?: string }) => {
  const [displayText, setDisplayText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);
  const [ref, isVisible] = useIntersectionObserver();

  useEffect(() => {
    if (!isVisible) return;

    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, delay);
      return () => clearTimeout(timeout);
    }
  }, [currentIndex, text, delay, isVisible]);

  return (
    <div ref={ref} className={className}>
      <span>{displayText}</span>
      <span className={`inline-block w-0.5 h-8 bg-current ml-1 ${currentIndex < text.length ? 'animate-blink' : 'opacity-0'}`} />
    </div>
  );
};

// Word reveal component
const WordReveal = ({ text, className = "" }: { text: string; className?: string }) => {
  const words = text.split(' ');
  const [ref, isVisible] = useIntersectionObserver();

  return (
    <div ref={ref} className={className}>
      {words.map((word, index) => (
        <span
          key={index}
          className={`inline-block mr-2 ${isVisible ? `animate-stagger-${Math.min(index % 6 + 1, 6)}` : 'opacity-0'} motion-reduce:animate-fade-in`}
        >
          {word}
        </span>
      ))}
    </div>
  );
};

// Scroll-triggered reveal components inspired by 1-light.eu
const ScrollReveal = ({
  children,
  animation = 'reveal-up',
  delay = 0,
  className = ""
}: {
  children: React.ReactNode;
  animation?: string;
  delay?: number;
  className?: string;
}) => {
  const [ref, isVisible] = useScrollReveal();

  return (
    <div
      ref={ref}
      className={`${className} ${isVisible ? animation : 'opacity-0'} motion-reduce:animate-fade-in`}
      style={{ animationDelay: `${delay}ms` }}
    >
      {children}
    </div>
  );
};

const SectionReveal = ({ children, className = "" }: { children: React.ReactNode; className?: string }) => {
  const [ref, isVisible] = useScrollReveal();

  return (
    <section
      ref={ref}
      className={`${className} ${isVisible ? 'animate-section-reveal' : 'opacity-0'} motion-reduce:animate-fade-in`}
    >
      {children}
    </section>
  );
};

const Catalogue = () => {
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [isCategoriesLoading, setIsCategoriesLoading] = useState(true);
  const [categories, setCategories] = useState<any[]>([]);

  useEffect(() => {
    // Load featured products
    fetch("/data/products.json")
      .then((res) => res.json())
      .then((products: Product[]) => {
        setFeaturedProducts(products.slice(0, 3));
      })
      .catch((error) => console.error("Error loading products:", error));
  }, []);

  useEffect(() => {
    fetchCategories()
      .then((data) => {
        setCategories(data);
        setIsCategoriesLoading(false);
      })
      .catch((error) => {
        console.error("Error fetching categories:", error);
        setIsCategoriesLoading(false);
      });
  }, []);


  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-black to-gray-900 text-white overflow-hidden">

        {/* Video Container - This will define the section height */}
        <div className="relative z-0">
          <video
            autoPlay
            muted
            loop
            playsInline
            className="w-full h-auto object-cover block"
            style={{ aspectRatio: '16/9' }}
            poster="https://images.unsplash.com/photo-1524484485831-a92ffc0de03f?w=1920&h=1080&fit=crop"
          >
            <source src="/assets/videos/catalogue.mp4" type="video/mp4" />
            {/* Fallback for browsers that don't support video */}
            Your browser does not support the video tag.
          </video>
          {/* Overlay to enhance text readability */}
          {/* <div className="absolute inset-0 bg-black opacity-50"></div> */}
        </div>

        {/* Floating light orbs for premium effect - Responsive positioning */}
        <div className="absolute top-10 left-4 sm:top-16 sm:left-16 lg:top-20 lg:left-20 w-16 h-16 sm:w-24 sm:h-24 lg:w-32 lg:h-32 bg-white/5 rounded-full blur-xl animate-float-delayed"></div>
        <div className="absolute bottom-16 right-4 sm:bottom-24 sm:right-24 lg:bottom-32 lg:right-32 w-12 h-12 sm:w-18 sm:h-18 lg:w-24 lg:h-24 bg-white/3 rounded-full blur-lg animate-float"></div>
        <div className="absolute top-1/2 left-2 sm:left-6 lg:left-10 w-8 h-8 sm:w-12 sm:h-12 lg:w-16 lg:h-16 bg-white/4 rounded-full blur-md animate-glow-pulse"></div>

        <div className="absolute bottom-0 left-0 right-0 flex justify-center z-10">
          <div className="text-center px-4 sm:px-6 lg:px-8 pb-8 sm:pb-10 lg:pb-12">
            <div className="flex flex-col gap-4 justify-center animate-hero-cta motion-reduce:animate-fade-in">
              <Button asChild className="bg-white text-black hover:bg-gray-700 hover:text-white transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                <Link to="#">
                  Download Catalogue
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>


    </div>
  );
};

export default Catalogue;
