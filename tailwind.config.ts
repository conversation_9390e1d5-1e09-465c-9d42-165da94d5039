
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				'fade-in': {
					'0%': {
						opacity: '0',
						transform: 'translateY(20px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				// Hero animations
				'hero-title': {
					'0%': {
						opacity: '0',
						transform: 'translateY(30px) scale(0.95)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0) scale(1)'
					}
				},
				'hero-subtitle': {
					'0%': {
						opacity: '0',
						transform: 'translateX(-30px) rotateX(15deg)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateX(0) rotateX(0deg)'
					}
				},
				'hero-cta': {
					'0%': {
						opacity: '0',
						transform: 'translateY(20px) scale(0.9) rotateZ(-2deg)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0) scale(1) rotateZ(0deg)'
					}
				},
				// Slide animations from different directions
				'slide-in-left': {
					'0%': {
						opacity: '0',
						transform: 'translateX(-100px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateX(0)'
					}
				},
				'slide-in-right': {
					'0%': {
						opacity: '0',
						transform: 'translateX(100px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateX(0)'
					}
				},
				'slide-in-diagonal': {
					'0%': {
						opacity: '0',
						transform: 'translate(-50px, 50px) scale(0.8)'
					},
					'100%': {
						opacity: '1',
						transform: 'translate(0, 0) scale(1)'
					}
				},
				// Scale and rotate combinations
				'scale-rotate-in': {
					'0%': {
						opacity: '0',
						transform: 'scale(0.3) rotate(-10deg)'
					},
					'50%': {
						opacity: '0.8',
						transform: 'scale(1.05) rotate(2deg)'
					},
					'100%': {
						opacity: '1',
						transform: 'scale(1) rotate(0deg)'
					}
				},
				'scale-bounce': {
					'0%': {
						opacity: '0',
						transform: 'scale(0.3)'
					},
					'50%': {
						opacity: '0.8',
						transform: 'scale(1.1)'
					},
					'70%': {
						transform: 'scale(0.95)'
					},
					'100%': {
						opacity: '1',
						transform: 'scale(1)'
					}
				},
				// Typewriter effect
				'typewriter': {
					'0%': {
						width: '0'
					},
					'100%': {
						width: '100%'
					}
				},
				'blink': {
					'0%, 50%': {
						borderColor: 'transparent'
					},
					'51%, 100%': {
						borderColor: 'currentColor'
					}
				},
				// Wave and ripple effects
				'wave-1': {
					'0%': {
						opacity: '0',
						transform: 'translateY(30px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				'wave-2': {
					'0%': {
						opacity: '0',
						transform: 'translateY(30px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				'wave-3': {
					'0%': {
						opacity: '0',
						transform: 'translateY(30px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				// Floating animations
				'float': {
					'0%, 100%': {
						transform: 'translateY(0px)'
					},
					'50%': {
						transform: 'translateY(-10px)'
					}
				},
				'float-delayed': {
					'0%, 100%': {
						transform: 'translateY(0px)'
					},
					'50%': {
						transform: 'translateY(-15px)'
					}
				},
				// Glow effect for premium feel
				'glow-pulse': {
					'0%, 100%': {
						boxShadow: '0 0 20px rgba(255, 255, 255, 0.1)'
					},
					'50%': {
						boxShadow: '0 0 40px rgba(255, 255, 255, 0.2)'
					}
				},
				// 1-light.eu inspired animations
				'reveal-up': {
					'0%': {
						opacity: '0',
						transform: 'translateY(40px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				'reveal-left': {
					'0%': {
						opacity: '0',
						transform: 'translateX(-40px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateX(0)'
					}
				},
				'reveal-right': {
					'0%': {
						opacity: '0',
						transform: 'translateX(40px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateX(0)'
					}
				},
				'content-reveal': {
					'0%': {
						opacity: '0',
						transform: 'translateY(20px) scale(0.95)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0) scale(1)'
					}
				},
				'section-reveal': {
					'0%': {
						opacity: '0',
						transform: 'translateY(60px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				// Additional animations for new sections
				'card-flip': {
					'0%': {
						opacity: '0',
						transform: 'rotateY(-15deg) translateY(20px)'
					},
					'100%': {
						opacity: '1',
						transform: 'rotateY(0deg) translateY(0)'
					}
				},
				'icon-bounce': {
					'0%': {
						opacity: '0',
						transform: 'scale(0.3) translateY(20px)'
					},
					'50%': {
						opacity: '0.8',
						transform: 'scale(1.1) translateY(-5px)'
					},
					'100%': {
						opacity: '1',
						transform: 'scale(1) translateY(0)'
					}
				},
				'testimonial-slide': {
					'0%': {
						opacity: '0',
						transform: 'translateX(-30px) scale(0.95)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateX(0) scale(1)'
					}
				},
				'process-step': {
					'0%': {
						opacity: '0',
						transform: 'translateY(30px) scale(0.9)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0) scale(1)'
					}
				},
				// Enhanced process animations with diversified patterns
				'process-step-diagonal': {
					'0%': {
						opacity: '0',
						transform: 'translate(-40px, 40px) scale(0.8) rotate(-5deg)'
					},
					'100%': {
						opacity: '1',
						transform: 'translate(0, 0) scale(1) rotate(0deg)'
					}
				},
				'process-step-bounce': {
					'0%': {
						opacity: '0',
						transform: 'translateY(60px) scale(0.3)'
					},
					'50%': {
						opacity: '0.8',
						transform: 'translateY(-10px) scale(1.05)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0) scale(1)'
					}
				},
				'process-step-rotate': {
					'0%': {
						opacity: '0',
						transform: 'translateY(30px) scale(0.5) rotate(180deg)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0) scale(1) rotate(0deg)'
					}
				},
				'process-step-elastic': {
					'0%': {
						opacity: '0',
						transform: 'translateY(50px) scaleY(0.6) scaleX(1.2)'
					},
					'60%': {
						opacity: '0.8',
						transform: 'translateY(-15px) scaleY(1.1) scaleX(0.9)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0) scaleY(1) scaleX(1)'
					}
				},
				// Process line animations
				'process-line-draw': {
					'0%': {
						strokeDasharray: '0 1000',
						opacity: '0'
					},
					'50%': {
						opacity: '1'
					},
					'100%': {
						strokeDasharray: '1000 0',
						opacity: '1'
					}
				},
				'process-line-glow': {
					'0%, 100%': {
						filter: 'drop-shadow(0 0 2px rgba(59, 130, 246, 0.3))'
					},
					'50%': {
						filter: 'drop-shadow(0 0 8px rgba(59, 130, 246, 0.6))'
					}
				},
				// Glass morphism card animations
				'glass-card-reveal': {
					'0%': {
						opacity: '0',
						transform: 'translateY(40px) scale(0.9)',
						backdropFilter: 'blur(0px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0) scale(1)',
						backdropFilter: 'blur(20px)'
					}
				},
				// Optimized icon hover animations
				'icon-float-optimized': {
					'0%, 100%': {
						transform: 'translate3d(0, 0, 0) rotate(0deg)',
						opacity: '1'
					},
					'50%': {
						transform: 'translate3d(0, -4px, 0) rotate(2deg)',
						opacity: '0.9'
					}
				},
				'icon-pulse-optimized': {
					'0%, 100%': {
						transform: 'scale3d(1, 1, 1)',
						opacity: '1'
					},
					'50%': {
						transform: 'scale3d(1.05, 1.05, 1)',
						opacity: '0.8'
					}
				},
				// Ripple effect animation
				'ripple': {
					'0%': {
						transform: 'scale(0)',
						opacity: '0.6'
					},
					'100%': {
						transform: 'scale(4)',
						opacity: '0'
					}
				},
				// Float animations for background elements
				'float': {
					'0%, 100%': {
						transform: 'translateY(0px) rotate(0deg)'
					},
					'50%': {
						transform: 'translateY(-20px) rotate(5deg)'
					}
				},
				'float-delayed': {
					'0%, 100%': {
						transform: 'translateY(0px) rotate(0deg)'
					},
					'50%': {
						transform: 'translateY(-15px) rotate(-3deg)'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fade-in 0.6s ease-out',
				// Hero animations with different timings and easing
				'hero-title': 'hero-title 1.2s cubic-bezier(0.4, 0, 0.2, 1)',
				'hero-subtitle': 'hero-subtitle 1s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both',
				'hero-cta': 'hero-cta 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0.6s both',
				// Directional slides with varied easing
				'slide-in-left': 'slide-in-left 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both',
				'slide-in-right': 'slide-in-right 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both',
				'slide-in-diagonal': 'slide-in-diagonal 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) both',
				// Scale and rotate with bounce
				'scale-rotate-in': 'scale-rotate-in 0.9s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
				'scale-bounce': 'scale-bounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
				// Typewriter effect
				'typewriter': 'typewriter 2s steps(40, end)',
				'blink': 'blink 1s infinite',
				// Wave effects with staggered delays
				'wave-1': 'wave-1 0.6s ease-out',
				'wave-2': 'wave-1 0.6s ease-out 0.1s both',
				'wave-3': 'wave-1 0.6s ease-out 0.2s both',
				'wave-4': 'wave-1 0.6s ease-out 0.3s both',
				'wave-5': 'wave-1 0.6s ease-out 0.4s both',
				'wave-6': 'wave-1 0.6s ease-out 0.5s both',
				// Floating animations
				'float': 'float 3s ease-in-out infinite',
				'float-delayed': 'float-delayed 4s ease-in-out infinite 1s',
				// Glow effect
				'glow-pulse': 'glow-pulse 2s ease-in-out infinite',
				// Staggered entrance animations
				'stagger-1': 'fade-in 0.6s ease-out',
				'stagger-2': 'fade-in 0.6s ease-out 0.1s both',
				'stagger-3': 'fade-in 0.6s ease-out 0.2s both',
				'stagger-4': 'fade-in 0.6s ease-out 0.3s both',
				'stagger-5': 'fade-in 0.6s ease-out 0.4s both',
				'stagger-6': 'fade-in 0.6s ease-out 0.5s both',
				// 1-light.eu inspired reveal animations
				'reveal-up': 'reveal-up 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both',
				'reveal-left': 'reveal-left 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both',
				'reveal-right': 'reveal-right 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both',
				'content-reveal': 'content-reveal 0.6s cubic-bezier(0.4, 0, 0.2, 1) both',
				'section-reveal': 'section-reveal 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) both',
				// Delayed reveal animations
				'reveal-up-delay-1': 'reveal-up 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s both',
				'reveal-up-delay-2': 'reveal-up 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s both',
				'reveal-up-delay-3': 'reveal-up 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.6s both',
				// New section animations
				'card-flip': 'card-flip 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) both',
				'icon-bounce': 'icon-bounce 0.9s cubic-bezier(0.68, -0.55, 0.265, 1.55) both',
				'testimonial-slide': 'testimonial-slide 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) both',
				'process-step': 'process-step 0.6s cubic-bezier(0.4, 0, 0.2, 1) both',
				// Staggered new section animations
				'card-flip-delay-1': 'card-flip 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) 0.1s both',
				'card-flip-delay-2': 'card-flip 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) 0.2s both',
				'card-flip-delay-3': 'card-flip 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) 0.3s both',
				'process-step-delay-1': 'process-step 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both',
				'process-step-delay-2': 'process-step 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both',
				'process-step-delay-3': 'process-step 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.6s both',
				'process-step-delay-4': 'process-step 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.8s both',
				// Enhanced process step animations with diversified patterns
				'process-step-diagonal-1': 'process-step-diagonal 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) 0.1s both',
				'process-step-bounce-2': 'process-step-bounce 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) 0.3s both',
				'process-step-rotate-3': 'process-step-rotate 0.9s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0.5s both',
				'process-step-elastic-4': 'process-step-elastic 1.1s cubic-bezier(0.68, -0.55, 0.265, 1.55) 0.7s both',
				// Process line animations
				'process-line-draw': 'process-line-draw 2s ease-in-out 0.5s both',
				'process-line-glow': 'process-line-glow 3s ease-in-out infinite',
				// Glass morphism animations
				'glass-card-reveal-1': 'glass-card-reveal 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s both',
				'glass-card-reveal-2': 'glass-card-reveal 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s both',
				'glass-card-reveal-3': 'glass-card-reveal 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.6s both',
				'glass-card-reveal-4': 'glass-card-reveal 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.8s both',
				// Optimized icon animations
				'icon-float': 'icon-float-optimized 2s ease-out infinite',
				'icon-pulse': 'icon-pulse-optimized 1.5s ease-out infinite',
				// Ripple effect
				'ripple': 'ripple 0.6s ease-out',
				// Float animations
				'float': 'float 6s ease-in-out infinite',
				'float-delayed': 'float-delayed 8s ease-in-out infinite 2s'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
