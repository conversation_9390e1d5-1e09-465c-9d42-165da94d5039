@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {

  /* Performance optimizations for animations */
  .transform-gpu {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
    will-change: transform;
  }

  /* Glass morphism utilities */
  .glass-card {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Enhanced shadow utilities */
  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
  }

  /* Glow effects */
  .glow-blue {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  }

  .glow-purple {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.4);
  }

  .glow-emerald {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.4);
  }

  .glow-orange {
    box-shadow: 0 0 20px rgba(249, 115, 22, 0.4);
  }

  /* Accessibility: Respect prefers-reduced-motion */
  @media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }

    /* Disable floating animations for reduced motion */
    .animate-float,
    .animate-float-delayed,
    .animate-glow-pulse {
      animation: none !important;
    }

    /* Provide alternative subtle animations */
    .motion-reduce\:animate-fade-in {
      animation: fade-in 0.3s ease-out !important;
    }
  }

  /* Ensure smooth 60fps animations */
  [class*="animate-"] {
    will-change: transform, opacity;
    transform: translateZ(0);
    animation-fill-mode: both;
  }

  /* Prevent animation restarts on hover */
  [class*="animate-slide-in-"] {
    animation-iteration-count: 1;
    animation-fill-mode: both;
  }

  /* Optimize hover states */
  .group:hover [class*="group-hover:"] {
    will-change: transform, opacity, background-color;
  }

  /* Smooth hover transitions for category cards */
  .swiper-slide>div {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Prevent hover state conflicts and animation restarts */
  .swiper-slide:hover>div {
    animation-play-state: paused;
  }

  /* Ensure entrance animations complete before hover effects */
  .swiper-slide>div[class*="animate-slide-in-"] {
    animation-duration: 0.8s;
    animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    animation-fill-mode: both;
    animation-iteration-count: 1;
  }

  /* Prevent glow animation conflicts */
  .hover\:animate-glow-pulse:hover {
    animation: none !important;
  }

  /* Professional hover states inspired by 1-light.eu */
  .professional-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .professional-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  /* Content hierarchy animations */
  .content-hierarchy h1 {
    animation-delay: 0ms;
  }

  .content-hierarchy h2 {
    animation-delay: 200ms;
  }

  .content-hierarchy p {
    animation-delay: 400ms;
  }

  .content-hierarchy .cta {
    animation-delay: 600ms;
  }

  /* Smooth scroll behavior */
  html {
    scroll-behavior: smooth;
  }

  /* Enhanced focus states for accessibility */
  button:focus-visible,
  a:focus-visible {
    outline: 2px solid currentColor;
    outline-offset: 2px;
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Media optimization */
  img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  /* Video placeholder styles */
  .video-placeholder {
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
      linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
      linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  }

  /* Interactive elements */
  .interactive-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .interactive-hover:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }
}